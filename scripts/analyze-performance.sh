#!/bin/bash

# Performance Analysis Script for BenefitLens
# Can be run inside Docker container or on host with Node.js

set -e

echo "🔍 BenefitLens Performance Analysis"
echo "=================================="

# Check if we're in a Docker container
if [ -f /.dockerenv ]; then
    echo "📦 Running inside Docker container"
    DOCKER_MODE=true
else
    echo "🖥️  Running on host system"
    DOCKER_MODE=false
fi

# Function to run commands in Docker if needed
run_cmd() {
    if [ "$DOCKER_MODE" = true ]; then
        eval "$1"
    else
        docker exec benefitlens-app sh -c "$1"
    fi
}

echo ""
echo "1. 📊 Bundle Size Analysis"
echo "========================="

# Check if bundle analyzer is available
if [ "$DOCKER_MODE" = true ]; then
    if npm list @next/bundle-analyzer >/dev/null 2>&1; then
        echo "✅ Bundle analyzer available"
        echo "🔨 Building with bundle analysis..."
        ANALYZE=true npm run build
        echo "📈 Bundle analysis complete! Check the generated report."
    else
        echo "❌ Bundle analyzer not installed. Install with: npm install @next/bundle-analyzer"
    fi
else
    echo "🔨 Running bundle analysis in Docker..."
    docker run --rm -v $(pwd):/app -w /app -e ANALYZE=true node:20-alpine sh -c '
        npm ci --only=production
        npm install @next/bundle-analyzer
        ANALYZE=true npm run build
    '
fi

echo ""
echo "2. 📦 Dependency Analysis"
echo "========================"

echo "📋 Checking for unused dependencies..."
if [ "$DOCKER_MODE" = true ]; then
    if command -v npx >/dev/null 2>&1; then
        npx depcheck --ignores="@types/*,eslint*,playwright,vitest,@testing-library/*"
    else
        echo "❌ npx not available"
    fi
else
    docker run --rm -v $(pwd):/app -w /app node:20-alpine sh -c '
        npm ci
        npx depcheck --ignores="@types/*,eslint*,playwright,vitest,@testing-library/*"
    '
fi

echo ""
echo "3. 🔍 Package Audit"
echo "=================="

echo "🛡️  Checking for security vulnerabilities..."
run_cmd "npm audit --audit-level=moderate"

echo ""
echo "4. 📈 Current Performance Metrics"
echo "================================"

# Check if the app is running
if curl -f http://localhost:3000/api/health >/dev/null 2>&1; then
    echo "✅ App is running, fetching performance metrics..."
    
    # Get basic health info
    echo "🏥 Health Status:"
    curl -s http://localhost:3000/api/health | jq '.' 2>/dev/null || curl -s http://localhost:3000/api/health
    
    echo ""
    echo "⚡ Performance Metrics:"
    curl -s http://localhost:3000/api/health/ready | jq '.' 2>/dev/null || curl -s http://localhost:3000/api/health/ready
    
else
    echo "❌ App is not running. Start with: npm run dev:start"
fi

echo ""
echo "5. 💾 Database Performance"
echo "========================="

# Check database connection and basic stats
if [ "$DOCKER_MODE" = true ]; then
    echo "🗄️  Database connection info:"
    echo "Use: docker exec benefitlens-postgres psql -U benefitlens_user -d benefitlens -c \"SELECT version();\""
else
    echo "🗄️  Checking database performance..."
    docker exec benefitlens-postgres psql -U benefitlens_user -d benefitlens -c "
        SELECT 
            'Database Size' as metric,
            pg_size_pretty(pg_database_size('benefitlens')) as value
        UNION ALL
        SELECT 
            'Active Connections' as metric,
            count(*)::text as value
        FROM pg_stat_activity 
        WHERE state = 'active'
        UNION ALL
        SELECT 
            'Cache Hit Ratio' as metric,
            round(100.0 * sum(blks_hit) / (sum(blks_hit) + sum(blks_read)), 2)::text || '%' as value
        FROM pg_stat_database 
        WHERE datname = 'benefitlens';
    " 2>/dev/null || echo "❌ Could not connect to database"
fi

echo ""
echo "6. 🚀 Performance Recommendations"
echo "================================"

echo "Based on your current setup:"
echo ""
echo "🎯 Quick Wins:"
echo "  • Run bundle analysis: npm run analyze:bundle:docker"
echo "  • Check unused deps: npm run analyze:deps:docker"
echo "  • Apply database indexes: psql < database/migrations/030-performance-optimization-indexes.sql"
echo ""
echo "🔧 Medium-term:"
echo "  • Implement lazy loading for heavy components"
echo "  • Add API response caching"
echo "  • Optimize images with Next.js Image component"
echo ""
echo "🏗️  Infrastructure:"
echo "  • Consider CDN for static assets"
echo "  • Monitor Raspberry Pi resource usage"
echo "  • Implement service worker for offline caching"

echo ""
echo "✅ Performance analysis complete!"
echo ""
echo "📊 To view bundle analysis:"
echo "   Open .next/analyze/client.html in a browser"
echo ""
echo "🔧 To apply optimizations:"
echo "   1. Review bundle analysis results"
echo "   2. Apply database migrations"
echo "   3. Implement recommended code changes"
