// Performance monitoring dashboard for admins
import { Suspense } from 'react'
import { requireAdmin } from '@/lib/auth'
import { performanceMonitor } from '@/lib/performance'
import { getPoolStats } from '@/lib/local-db'
import { checkCacheHealth } from '@/lib/postgresql-cache'

async function getPerformanceData() {
  const [metrics, poolStats, cacheHealth] = await Promise.all([
    performanceMonitor.collectMetrics(),
    getPoolStats(),
    checkCacheHealth()
  ])
  
  return { metrics, poolStats, cacheHealth }
}

function PerformanceMetrics({ data }: { data: Awaited<ReturnType<typeof getPerformanceData>> }) {
  const { metrics, poolStats, cacheHealth } = data
  
  return (
    <div className="space-y-6">
      {/* Memory Usage */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4">Memory Usage</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {metrics.memory.heapUsedMB.toFixed(1)}MB
            </div>
            <div className="text-sm text-gray-600">Heap Used</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {metrics.memory.heapTotalMB.toFixed(1)}MB
            </div>
            <div className="text-sm text-gray-600">Heap Total</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {metrics.memory.rssMB.toFixed(1)}MB
            </div>
            <div className="text-sm text-gray-600">RSS</div>
          </div>
          <div className="text-center">
            <div className={`text-2xl font-bold ${metrics.eventLoop.delay > 100 ? 'text-red-600' : 'text-green-600'}`}>
              {metrics.eventLoop.delay.toFixed(1)}ms
            </div>
            <div className="text-sm text-gray-600">Event Loop Delay</div>
          </div>
        </div>
      </div>

      {/* Database Performance */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4">Database Performance</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {metrics.database.totalConnections}
            </div>
            <div className="text-sm text-gray-600">Total Connections</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {metrics.database.idleConnections}
            </div>
            <div className="text-sm text-gray-600">Idle Connections</div>
          </div>
          <div className="text-center">
            <div className={`text-2xl font-bold ${metrics.database.waitingConnections > 5 ? 'text-red-600' : 'text-green-600'}`}>
              {metrics.database.waitingConnections}
            </div>
            <div className="text-sm text-gray-600">Waiting</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {metrics.database.maxConnections}
            </div>
            <div className="text-sm text-gray-600">Max Connections</div>
          </div>
        </div>
      </div>

      {/* Cache Status */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4">Cache Status</h3>
        <div className="flex items-center space-x-4">
          <div className={`w-4 h-4 rounded-full ${cacheHealth ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <span className={`font-semibold ${cacheHealth ? 'text-green-600' : 'text-red-600'}`}>
            {cacheHealth ? 'Healthy' : 'Unhealthy'}
          </span>
        </div>
      </div>

      {/* System Info */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4">System Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <div className="text-sm text-gray-600">Uptime</div>
            <div className="text-lg font-semibold">
              {Math.floor(metrics.uptime / 3600)}h {Math.floor((metrics.uptime % 3600) / 60)}m
            </div>
          </div>
          <div>
            <div className="text-sm text-gray-600">Node.js Version</div>
            <div className="text-lg font-semibold">{process.version}</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default async function PerformancePage() {
  await requireAdmin()
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Performance Dashboard</h1>
        <p className="text-gray-600">Monitor application performance and system health</p>
      </div>
      
      <Suspense fallback={
        <div className="animate-pulse space-y-6">
          <div className="bg-gray-200 h-32 rounded-lg"></div>
          <div className="bg-gray-200 h-32 rounded-lg"></div>
          <div className="bg-gray-200 h-32 rounded-lg"></div>
        </div>
      }>
        <PerformanceData />
      </Suspense>
    </div>
  )
}

async function PerformanceData() {
  const data = await getPerformanceData()
  return <PerformanceMetrics data={data} />
}
