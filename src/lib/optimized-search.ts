// Optimized search functions for better performance
import { query } from './local-db'
import { getCache, setCache } from './postgresql-cache'
import { logger } from './logger'
import { PerformanceTimer } from './performance'

interface SearchOptions {
  useCache?: boolean
  cacheTTL?: number
  limit?: number
  offset?: number
}

// Optimized company search with full-text search and caching
export async function optimizedSearchCompanies(
  searchQuery: string, 
  sort: 'smart' | 'alphabetical' | 'newest' = 'smart',
  options: SearchOptions = {}
) {
  const timer = new PerformanceTimer('optimized_company_search')
  const { useCache = true, cacheTTL = 300, limit = 50, offset = 0 } = options
  
  // Create cache key
  const cacheKey = `search:companies:${searchQuery}:${sort}:${limit}:${offset}`
  
  try {
    // Try cache first
    if (useCache) {
      const cached = await getCache(cacheKey)
      if (cached) {
        timer.end({ cache: 'hit', query: searchQuery })
        return cached
      }
    }
    
    // Use full-text search for better performance
    const sql = `
      WITH ranked_companies AS (
        SELECT
          c.*,
          -- Full-text search ranking
          ts_rank(
            to_tsvector('english', c.name || ' ' || COALESCE(c.description, '')),
            plainto_tsquery('english', $1)
          ) as search_rank,
          -- Benefit count for smart ranking
          COALESCE(benefit_counts.verified_count, 0) as verified_benefit_count,
          COALESCE(benefit_counts.total_count, 0) as total_benefit_count
        FROM companies c
        LEFT JOIN (
          SELECT 
            company_id,
            COUNT(*) FILTER (WHERE is_verified = true) as verified_count,
            COUNT(*) as total_count
          FROM company_benefits
          GROUP BY company_id
        ) benefit_counts ON c.id = benefit_counts.company_id
        WHERE 
          to_tsvector('english', c.name || ' ' || COALESCE(c.description, '')) @@ plainto_tsquery('english', $1)
          OR c.name ILIKE $2
          OR EXISTS (
            SELECT 1 FROM company_benefits cb
            JOIN benefits b ON cb.benefit_id = b.id
            WHERE cb.company_id = c.id AND b.name ILIKE $2
          )
      ),
      companies_with_data AS (
        SELECT 
          rc.*,
          COALESCE(benefits_agg.company_benefits, '[]'::json) as company_benefits,
          COALESCE(locations_agg.locations, '[]'::json) as locations
        FROM ranked_companies rc
        LEFT JOIN (
          SELECT
            cb.company_id,
            json_agg(
              json_build_object(
                'id', cb.id,
                'benefit_id', cb.benefit_id,
                'is_verified', cb.is_verified,
                'benefit', json_build_object(
                  'id', b.id,
                  'name', b.name,
                  'category_id', b.category_id,
                  'icon', b.icon
                )
              ) ORDER BY cb.is_verified DESC, b.name ASC
            ) as company_benefits
          FROM company_benefits cb
          JOIN benefits b ON cb.benefit_id = b.id
          WHERE cb.company_id IN (SELECT id FROM ranked_companies)
          GROUP BY cb.company_id
        ) benefits_agg ON rc.id = benefits_agg.company_id
        LEFT JOIN (
          SELECT
            cl.company_id,
            json_agg(
              json_build_object(
                'id', cl.id,
                'city', cl.city,
                'country', cl.country,
                'country_code', cl.country_code,
                'location_type', cl.location_type,
                'is_primary', cl.is_primary
              ) ORDER BY cl.is_primary DESC, cl.is_headquarters DESC
            ) as locations
          FROM company_locations cl
          WHERE cl.company_id IN (SELECT id FROM ranked_companies)
          GROUP BY cl.company_id
        ) locations_agg ON rc.id = locations_agg.company_id
      )
      SELECT * FROM companies_with_data
      ORDER BY 
        CASE 
          WHEN $3 = 'smart' THEN (search_rank * 0.4 + (verified_benefit_count * 0.6))
          WHEN $3 = 'alphabetical' THEN 0
          WHEN $3 = 'newest' THEN EXTRACT(EPOCH FROM created_at)
          ELSE search_rank
        END DESC,
        CASE WHEN $3 = 'alphabetical' THEN name END ASC
      LIMIT $4 OFFSET $5
    `
    
    const searchPattern = `%${searchQuery}%`
    const result = await query(sql, [searchQuery, searchPattern, sort, limit, offset])
    
    // Cache the result
    if (useCache && result.rows.length > 0) {
      await setCache(cacheKey, result.rows, cacheTTL)
    }
    
    timer.end({ 
      cache: 'miss', 
      query: searchQuery, 
      resultCount: result.rows.length,
      sort 
    })
    
    return result.rows
    
  } catch (error) {
    timer.end({ error: true, query: searchQuery })
    logger.error('Optimized search failed', { error: error as Error, searchQuery })
    throw error
  }
}

// Optimized benefit search with autocomplete
export async function optimizedBenefitSearch(
  searchQuery: string,
  limit: number = 10
) {
  const timer = new PerformanceTimer('optimized_benefit_search')
  const cacheKey = `search:benefits:${searchQuery}:${limit}`
  
  try {
    // Try cache first
    const cached = await getCache(cacheKey)
    if (cached) {
      timer.end({ cache: 'hit', query: searchQuery })
      return cached
    }
    
    const sql = `
      SELECT 
        b.*,
        bc.name as category_name,
        COUNT(cb.company_id) as company_count,
        ts_rank(
          to_tsvector('english', b.name || ' ' || COALESCE(b.description, '')),
          plainto_tsquery('english', $1)
        ) as search_rank
      FROM benefits b
      LEFT JOIN benefit_categories bc ON b.category_id = bc.id
      LEFT JOIN company_benefits cb ON b.id = cb.benefit_id
      WHERE 
        to_tsvector('english', b.name || ' ' || COALESCE(b.description, '')) @@ plainto_tsquery('english', $1)
        OR b.name ILIKE $2
      GROUP BY b.id, bc.name
      ORDER BY search_rank DESC, company_count DESC, b.name ASC
      LIMIT $3
    `
    
    const searchPattern = `%${searchQuery}%`
    const result = await query(sql, [searchQuery, searchPattern, limit])
    
    // Cache for 10 minutes
    await setCache(cacheKey, result.rows, 600)
    
    timer.end({ 
      cache: 'miss', 
      query: searchQuery, 
      resultCount: result.rows.length 
    })
    
    return result.rows
    
  } catch (error) {
    timer.end({ error: true, query: searchQuery })
    logger.error('Optimized benefit search failed', { error: error as Error, searchQuery })
    throw error
  }
}

// Search suggestions with debouncing
export async function getSearchSuggestions(
  searchQuery: string,
  type: 'companies' | 'benefits' | 'locations' = 'companies',
  limit: number = 5
) {
  if (searchQuery.length < 2) return []
  
  const cacheKey = `suggestions:${type}:${searchQuery}:${limit}`
  
  try {
    // Try cache first (short TTL for suggestions)
    const cached = await getCache(cacheKey)
    if (cached) return cached
    
    let sql = ''
    let params: any[] = []
    
    switch (type) {
      case 'companies':
        sql = `
          SELECT name, id
          FROM companies
          WHERE name ILIKE $1
          ORDER BY name ASC
          LIMIT $2
        `
        params = [`%${searchQuery}%`, limit]
        break
        
      case 'benefits':
        sql = `
          SELECT name, id
          FROM benefits
          WHERE name ILIKE $1
          ORDER BY name ASC
          LIMIT $2
        `
        params = [`%${searchQuery}%`, limit]
        break
        
      case 'locations':
        sql = `
          SELECT DISTINCT city || ', ' || country as name, city, country
          FROM company_locations
          WHERE city ILIKE $1 OR country ILIKE $1
          ORDER BY city ASC
          LIMIT $2
        `
        params = [`%${searchQuery}%`, limit]
        break
    }
    
    const result = await query(sql, params)
    
    // Cache for 5 minutes
    await setCache(cacheKey, result.rows, 300)
    
    return result.rows
    
  } catch (error) {
    logger.error('Search suggestions failed', { error: error as Error, searchQuery, type })
    return []
  }
}
