// API Response Caching Middleware
import { NextRequest, NextResponse } from 'next/server'
import { getCache, setCache } from './postgresql-cache'
import { logger } from './logger'

interface CacheConfig {
  ttl: number // Time to live in seconds
  varyBy?: string[] // Headers to vary cache by
  skipCache?: boolean
}

const DEFAULT_CACHE_CONFIG: CacheConfig = {
  ttl: 300, // 5 minutes
  varyBy: ['user-agent'],
  skipCache: false
}

// Cache configurations for different endpoints
const ENDPOINT_CACHE_CONFIG: Record<string, CacheConfig> = {
  '/api/benefits': { ttl: 3600, varyBy: [] }, // 1 hour, no variation
  '/api/companies': { ttl: 300, varyBy: ['authorization'] }, // 5 minutes, vary by user
  '/api/search': { ttl: 600, varyBy: ['authorization'] }, // 10 minutes
  '/api/locations/suggestions': { ttl: 1800, varyBy: [] }, // 30 minutes
  '/api/analytics': { ttl: 900, varyBy: ['authorization'] }, // 15 minutes
}

function getCacheKey(request: NextRequest, config: CacheConfig): string {
  const url = new URL(request.url)
  const pathname = url.pathname
  const searchParams = url.searchParams.toString()
  
  // Include vary headers in cache key
  const varyValues = config.varyBy?.map(header => 
    request.headers.get(header) || ''
  ).join('|') || ''
  
  return `api_cache:${pathname}:${searchParams}:${varyValues}`
}

export function withApiCache(
  handler: (request: NextRequest) => Promise<NextResponse>,
  customConfig?: Partial<CacheConfig>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    // Only cache GET requests
    if (request.method !== 'GET') {
      return handler(request)
    }

    const url = new URL(request.url)
    const pathname = url.pathname
    
    // Get cache configuration
    const endpointConfig = ENDPOINT_CACHE_CONFIG[pathname] || {}
    const config = { ...DEFAULT_CACHE_CONFIG, ...endpointConfig, ...customConfig }
    
    if (config.skipCache) {
      return handler(request)
    }

    const cacheKey = getCacheKey(request, config)
    
    try {
      // Try to get from cache
      const cachedResponse = await getCache(cacheKey)
      
      if (cachedResponse) {
        logger.debug('API cache hit', { pathname, cacheKey })
        
        return new NextResponse(cachedResponse.body, {
          status: cachedResponse.status,
          headers: {
            ...cachedResponse.headers,
            'X-Cache': 'HIT',
            'X-Cache-Key': cacheKey
          }
        })
      }
      
      // Cache miss - execute handler
      logger.debug('API cache miss', { pathname, cacheKey })
      const response = await handler(request)
      
      // Only cache successful responses
      if (response.status >= 200 && response.status < 300) {
        const responseBody = await response.text()
        const responseHeaders = Object.fromEntries(response.headers.entries())
        
        // Store in cache
        await setCache(cacheKey, {
          body: responseBody,
          status: response.status,
          headers: responseHeaders
        }, config.ttl)
        
        // Return response with cache headers
        return new NextResponse(responseBody, {
          status: response.status,
          headers: {
            ...responseHeaders,
            'X-Cache': 'MISS',
            'X-Cache-Key': cacheKey,
            'Cache-Control': `public, max-age=${config.ttl}, s-maxage=${config.ttl * 2}`
          }
        })
      }
      
      return response
      
    } catch (error) {
      logger.error('API cache error', { error: error as Error, pathname, cacheKey })
      // Fallback to handler on cache error
      return handler(request)
    }
  }
}

// Cache invalidation helper
export async function invalidateApiCache(pattern: string): Promise<void> {
  try {
    // This would need to be implemented in postgresql-cache.ts
    // await clearCachePattern(`api_cache:${pattern}*`)
    logger.info('API cache invalidated', { pattern })
  } catch (error) {
    logger.error('Failed to invalidate API cache', { error: error as Error, pattern })
  }
}
