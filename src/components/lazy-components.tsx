// Lazy-loaded components for better performance
import dynamic from 'next/dynamic'
import { Suspense } from 'react'

// Lazy load heavy components
export const LazyBenefitRanking = dynamic(
  () => import('./benefit-ranking').then(mod => ({ default: mod.BenefitRanking })),
  {
    loading: () => <div className="animate-pulse bg-gray-200 h-64 rounded-lg" />,
    ssr: false // Client-side only for interactive features
  }
)

export const LazyAnalyticsDashboard = dynamic(
  () => import('./analytics-dashboard').then(mod => ({ default: mod.AnalyticsDashboard })),
  {
    loading: () => <div className="animate-pulse bg-gray-200 h-96 rounded-lg" />,
    ssr: false
  }
)

export const LazyCompanyMap = dynamic(
  () => import('./company-map').then(mod => ({ default: mod.CompanyMap })),
  {
    loading: () => <div className="animate-pulse bg-gray-200 h-80 rounded-lg" />,
    ssr: false
  }
)

// Lazy load drag-and-drop functionality
export const LazyDragDropProvider = dynamic(
  () => import('@hello-pangea/dnd').then(mod => ({ default: mod.DragDropContext })),
  {
    ssr: false
  }
)

// Wrapper component with Suspense
export function LazyComponentWrapper({ 
  children, 
  fallback = <div className="animate-pulse bg-gray-200 h-32 rounded-lg" /> 
}: { 
  children: React.ReactNode
  fallback?: React.ReactNode 
}) {
  return (
    <Suspense fallback={fallback}>
      {children}
    </Suspense>
  )
}
