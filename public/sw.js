// Service Worker for BenefitLens
// Provides offline caching and performance improvements

const CACHE_NAME = 'benefitlens-v1'
const STATIC_CACHE_NAME = 'benefitlens-static-v1'
const API_CACHE_NAME = 'benefitlens-api-v1'

// Static assets to cache
const STATIC_ASSETS = [
  '/',
  '/favicon.ico',
  '/manifest.json',
  // Add other static assets
]

// API endpoints to cache
const CACHEABLE_APIS = [
  '/api/benefits',
  '/api/companies',
  '/api/locations/suggestions'
]

// Install event - cache static assets
self.addEventListener('install', (event) => {
  event.waitUntil(
    Promise.all([
      caches.open(STATIC_CACHE_NAME).then((cache) => {
        return cache.addAll(STATIC_ASSETS)
      }),
      caches.open(API_CACHE_NAME)
    ])
  )
  self.skipWaiting()
})

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME && 
              cacheName !== STATIC_CACHE_NAME && 
              cacheName !== API_CACHE_NAME) {
            return caches.delete(cacheName)
          }
        })
      )
    })
  )
  self.clients.claim()
})

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return
  }

  // Handle API requests
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleApiRequest(request))
    return
  }

  // Handle static assets
  if (url.pathname.startsWith('/_next/static/') || 
      url.pathname === '/favicon.ico' ||
      url.pathname === '/manifest.json') {
    event.respondWith(handleStaticAsset(request))
    return
  }

  // Handle page requests
  event.respondWith(handlePageRequest(request))
})

// Cache-first strategy for static assets
async function handleStaticAsset(request) {
  const cache = await caches.open(STATIC_CACHE_NAME)
  const cachedResponse = await cache.match(request)
  
  if (cachedResponse) {
    return cachedResponse
  }
  
  try {
    const networkResponse = await fetch(request)
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone())
    }
    return networkResponse
  } catch (error) {
    console.error('Failed to fetch static asset:', error)
    return new Response('Asset not available', { status: 404 })
  }
}

// Stale-while-revalidate for API requests
async function handleApiRequest(request) {
  const url = new URL(request.url)
  const isCacheable = CACHEABLE_APIS.some(api => url.pathname.startsWith(api))
  
  if (!isCacheable) {
    return fetch(request)
  }
  
  const cache = await caches.open(API_CACHE_NAME)
  const cachedResponse = await cache.match(request)
  
  // Start fetch in background
  const fetchPromise = fetch(request).then(async (networkResponse) => {
    if (networkResponse.ok) {
      // Only cache successful responses
      cache.put(request, networkResponse.clone())
    }
    return networkResponse
  }).catch(() => {
    // Return cached response on network error
    return cachedResponse
  })
  
  // Return cached response immediately if available
  if (cachedResponse) {
    // Update cache in background
    fetchPromise.catch(() => {}) // Ignore errors for background update
    return cachedResponse
  }
  
  // Wait for network if no cache
  return fetchPromise
}

// Network-first strategy for pages
async function handlePageRequest(request) {
  try {
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME)
      cache.put(request, networkResponse.clone())
    }
    
    return networkResponse
  } catch (error) {
    // Fallback to cache
    const cache = await caches.open(CACHE_NAME)
    const cachedResponse = await cache.match(request)
    
    if (cachedResponse) {
      return cachedResponse
    }
    
    // Return offline page if available
    return cache.match('/') || new Response('Offline', { status: 503 })
  }
}

// Background sync for analytics
self.addEventListener('sync', (event) => {
  if (event.tag === 'analytics-sync') {
    event.waitUntil(syncAnalytics())
  }
})

async function syncAnalytics() {
  // Implement analytics sync logic
  console.log('Syncing analytics data...')
}
