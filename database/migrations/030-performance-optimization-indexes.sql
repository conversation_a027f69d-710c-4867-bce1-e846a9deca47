-- Migration: Performance optimization indexes
-- Date: 2025-09-05
-- Description: Add additional indexes for performance optimization

-- Composite index for company search with filters
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_companies_search_composite
ON companies (industry, size, name);

-- Full-text search index for company names and descriptions
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_companies_fulltext_search
ON companies USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')));

-- Index for benefit search interactions analytics
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_benefit_search_interactions_analytics
ON benefit_search_interactions (created_at, company_id, benefit_id);

-- Index for company page views analytics
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_company_page_views_analytics
ON company_page_views (created_at, company_id, user_id);

-- Index for search queries analytics
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_search_queries_analytics
ON search_queries (created_at, query_text);

-- Partial index for verified benefits only
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_company_benefits_verified_only
ON company_benefits (company_id, benefit_id) WHERE is_verified = true;

-- Index for company locations by type
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_company_locations_by_type
ON company_locations (location_type, city, country);

-- Index for user sessions cleanup
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_sessions_cleanup
ON user_sessions (expires_at) WHERE expires_at < NOW();

-- Index for cache cleanup
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cache_entries_cleanup
ON cache_entries (expires_at) WHERE expires_at < NOW();

-- Log migration
INSERT INTO migration_log (id, migration_name, description, applied_at)
VALUES (30, '030-performance-optimization-indexes', 'Add performance optimization indexes', NOW());
